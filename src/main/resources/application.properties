spring.application.name=joe.bi

# H2
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
# Datasource
spring.datasource.url=jdbc:h2:file:~/databasefile
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=update


spring.jpa.show-sql=false
spring.jpa.open-in-view=false
server.port=${PORT:8080}
spring.main.allow-circular-references=true
debug=false

# Supported assets (comma-separated list)
joe.use-binance=false
joe.use-bybit=true

joe.params.deletePrevious=true
joe.params.dryOrders=false
joe.params.useMarketPrice=true
joe.params.alwaysBuy=false
joe.params.alwaysSell=false
joe.params.usdAvailable=20
joe.params.usdEarnedExitValue=1
spring.task.scheduling.pool.size=100

# Failed trades penalties
joe.params.failed1SleepsMin=30
joe.params.failed2SleepsMin=60
joe.params.failed2ReducedUsdAvailablePercent=50
joe.params.failed3SleepsMin=180
joe.params.failed3ReducedUsdAvailablePercent=25

joe.params.PriceSignal.disabledBuyCheck=true
joe.params.PriceSignal.disabledSellCheck=true

# Klines parameters to buy
joe.params.klines.interval=1m
joe.params.klines.limit=15

# Klines parameters to sell
joe.params.klines.short-term.interval=1s
joe.params.klines.short-term.limit=60

trader.buyer.fixed-rate=110000
trader.buyer.initial-delay=30000
trader.seller.fixed-rate=60000

# Binance API credentials (replace with your actual keys)
joe.api.key=xu2ERhzBo6ZZJjUw2XJa4E4HLpZzjqZy2qBmdGWIN9PNUnlwbmjIvJbgn9WwmDUA
joe.api.secret=50i4bTY4t4IoeMyb7aKWbIFk8tbdyyClXhSeCqEeeCKkvfZdaHebYQZck8kfYgiN

# TA-API credentials (replace with your actual key)
joe.taapi.io.key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbHVlIjoiNjgyMzc4MWQ4MDZmZjE2NTFlY2YzOWE4IiwiaWF0IjoxNzQ3MTU2NTU2LCJleHAiOjMzMjUxNjIwNTU2fQ.Yz7FksB6EmS3tCSy-yFF9ZMYNvi9I0Xul8Ut7r7bAKY

# Bybit API credentials (replace with your actual keys)
bybit.api.key=Jcii3xsB4gbFHdjJWd
bybit.api.secret=f07i5zu1l1gKO10o14f69lcoPRI90aacHRuD

# ChatGPT API
joe.chatgpt.api.key=********************************************************************************************************************************************************************


logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.joe.trader=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{36}.%M %L \\(%line\\) - %msg%n - %m%n at %F:%L
logging.level.com.fasterxml.jackson=ERROR
logging.level.okhttp3=INFO
logging.file.name=/users/jose.paz/logs/trader.log
logging.pattern.file= "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"