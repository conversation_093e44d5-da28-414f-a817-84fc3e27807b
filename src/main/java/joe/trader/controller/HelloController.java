package joe.trader.controller;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import joe.trader.client.JoeSpotClient;
import joe.trader.client.Trader;
import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.markets.ByBitExchange;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbStatus;
import joe.trader.entity.service.TradeService;
import joe.trader.repo.DbOrderRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequiredArgsConstructor
public class HelloController {

  final JoeSpotClient joeSpotClient;
//  final BinanceExchange binanceExchange;
  final ByBitExchange bybitExchange;
  final DbOrderRepo dbOrderRepo;
  final TradeService tradeService;

  final Trader trader;

  @Value("${joe.use-bybit:false}")
  boolean isUseBybit;

  @GetMapping(path = "/init", produces = "application/json")
  public AssetBuyDTO[] init() {

    if (isUseBybit) {
      bybitExchange.init();
    }

    return new AssetBuyDTO[]{
        bybitExchange.checkBuyOrder(SYMBOL.HUSDT)};
  }

  @GetMapping(path = "/hOrders", produces = "application/json")
  public List<DbOrder> getOrders() {

    List<DbOrder> binanceSOLOrders = getOrders(EXCHANGE.BINANCE);
    List<DbOrder> bybitSOLOrders = getOrders(EXCHANGE.BYBIT);

    return Stream.of(binanceSOLOrders, bybitSOLOrders).flatMap(Collection::stream).toList();
  }

  @GetMapping(path = "/dbStatus", produces = "application/json")
  public List<DbStatus> getDbStatus() {
    return joeSpotClient.getDbStatus();
  }

  @GetMapping(path = "/sumDbOrderDeltas", produces = "application/json")
  public String getDbDeltas() {
    return joeSpotClient.getDBDeltas();
  }

  @GetMapping(path = "/checkSellOrder", produces = "application/json")
  public AssetSellDTO[] checkSellOrder() {
     return new AssetSellDTO[]{bybitExchange.checkSellOrder()};
  }

  @DeleteMapping(path = "/orders")
  public void deleteAll() {
    tradeService.deleteAllOrders(EXCHANGE.BYBIT);
  }

  @DeleteMapping(path = "/dbStatus")
  public void deleteAllDbStatus() {
    joeSpotClient.deleteAllDbStatus();
  }

  public List<DbOrder> getOrders(EXCHANGE exchange) {
    return dbOrderRepo.findAllByExchangeEqualsOrderByIdDesc(exchange.name());
  }
}