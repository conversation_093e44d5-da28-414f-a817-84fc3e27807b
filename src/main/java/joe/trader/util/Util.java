package joe.trader.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import joe.trader.entity.DbPrice;
import joe.trader.repo.DbPriceRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@RequiredArgsConstructor
public class Util {

  final DbPriceRepo dbPriceRepo;

  public Object ifNull(Object object, Object replacement) {
      return object != null ? object : replacement;
  }

  public DbPrice parseDBPrice(String asset, String data) {
    final BigDecimal assetPrice = new BigDecimal(new JSONObject(data).get("lastPrice").toString());
    final BigDecimal lowPrice = new BigDecimal(new JSONObject(data).get("lowPrice").toString());
    final BigDecimal highPrice = new BigDecimal(new JSONObject(data).get("highPrice").toString());

    return DbPrice.builder()
      .asset(asset)
      .price(assetPrice)
      .lowPrice(lowPrice)
      .highPrice(highPrice)
      .timestamp(new Date())
      .build();

  }

  public List<DbPrice> parseKlines(long idDbStatus, String asset, String data) {

    final JSONArray klines = new JSONArray(data);
    List<DbPrice> dbPrices = new ArrayList<>();
    klines.forEach(( kline) -> {
      JSONArray klineArray = (JSONArray) kline;

      long timestamp = klineArray.getLong(0);
      BigDecimal lowPrice = new BigDecimal(klineArray.getString(3));
      BigDecimal highPrice = new BigDecimal(klineArray.getString(2));
      BigDecimal price = new BigDecimal(klineArray.getString(4));

      dbPrices.add(DbPrice.builder().asset(asset).timestamp(new Date(timestamp))
          .idDbStatus(idDbStatus)
          .price(price)
          .lowPrice(lowPrice)
          .highPrice(highPrice)
          .build());
    });

    log.info("got {} klines for {}", dbPrices.size(), asset);

    return dbPrices;
  }

  public double polyRegression(double[] x, double[] y) {
    int n = x.length;
    double xm = Arrays.stream(x).average().orElse(Double.NaN);
    double ym = Arrays.stream(y).average().orElse(Double.NaN);
    double x2m = Arrays.stream(x).map(a -> a * a).average().orElse(Double.NaN);
    double x3m = Arrays.stream(x).map(a -> a * a * a).average().orElse(Double.NaN);
    double x4m = Arrays.stream(x).map(a -> a * a * a * a).average().orElse(Double.NaN);
    double xym = 0.0;
    for (int i = 0; i < x.length && i < y.length; ++i) {
      xym += x[i] * y[i];
    }
    xym /= Math.min(x.length, y.length);
    double x2ym = 0.0;
    for (int i = 0; i < x.length && i < y.length; ++i) {
      x2ym += x[i] * x[i] * y[i];
    }
    x2ym /= Math.min(x.length, y.length);

    double sxx = x2m - xm * xm;
    double sxy = xym - xm * ym;
    double sxx2 = x3m - xm * x2m;
    double sx2x2 = x4m - x2m * x2m;
    double sx2y = x2ym - x2m * ym;

    double b = (sxy * sx2x2 - sx2y * sxx2) / (sxx * sx2x2 - sxx2 * sxx2);
    double c = (sx2y * sxx - sxy * sxx2) / (sxx * sx2x2 - sxx2 * sxx2);
    double a = ym - b * xm - c * x2m;

    log.debug("y = " + a + " + " + b + "x + " + c + "x^2");
    return c;

  }

  public void persistsDbPrices(List<DbPrice> dbPrices) {
    dbPriceRepo.saveAll(dbPrices);
  }

  public BigDecimal getNormalizedQuantity(BigDecimal quantity, BigDecimal stepSize) {
    BigDecimal normalizedQuantity;
    if (stepSize != null && stepSize.compareTo(BigDecimal.ZERO) > 0) {
      // Calculate the number of steps
      BigDecimal steps = quantity.divide(stepSize, 12, RoundingMode.DOWN);
      // Calculate the normalized quantity
      normalizedQuantity = steps.multiply(stepSize);
    } else {
      normalizedQuantity = quantity;
    }
    int decimalsQuantity = 3;
    return normalizedQuantity.setScale(decimalsQuantity, RoundingMode.HALF_UP);
  }

  public <T> T logR(T any) {
    log.debug("Returning {} : {}",any.getClass().getSimpleName(), any);
    return any;
  }
}
