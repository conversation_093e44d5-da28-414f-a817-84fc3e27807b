package joe.trader.chat;

import org.springframework.stereotype.Component;

import io.github.natanimn.BotClient;
import io.github.natanimn.filters.Filter;

@Component
public class EchoBot {
  private static final String TOKEN = "7728176526:AAEwoZK08p-7oGQ9sUUIajTZtdeoiq2F-fs";
  final BotClient bot = new BotClient(TOKEN);


  public void start() {
    // Listening for /start command
    bot.onMessage(filter -> filter.commands("start"), (context, message) -> {
      context.reply("Welcome!").exec();
    });

    // Listening for any incoming text
    bot.onMessage(Filter::text, (context, message) -> {
      System.out.println("Message id:" + message.chat.id);

      context.reply(message.text).exec();
    });

    bot.run(); // finally run the bot
  }

  public void sendMessage(String message) {
    return;
  }
}
