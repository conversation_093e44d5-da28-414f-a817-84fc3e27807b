package joe.trader.repo;

import java.util.List;

import org.springframework.data.jpa.repository.NativeQuery;
import org.springframework.data.repository.CrudRepository;

import joe.trader.entity.DbOrder;

public interface DbOrderRepo extends CrudRepository<DbOrder, Long> {
  DbOrder findById(long id);
  List<DbOrder> findBySoldPriceIsNull();
  List<DbOrder> findAllByOrderByIdDesc();

  List<DbOrder> findBySoldPriceIsNullAndAssetEquals(String asset);

  @NativeQuery("SELECT CONCAT(b.asset,'|',round(sum(b.delta*b.quantity),2),'USD')  res FROM DbOrder b group by b.asset")
  List<String> getDBDeltas();

  List<DbOrder> findAllByAssetEqualsAndExchangeEqualsOrderByIdDesc(String symbol, String exchange);
  List<DbOrder> findAllByExchangeEqualsOrderByIdDesc(String exchange);

  void deleteAllByExchangeEquals(String exchange);

  List<DbOrder> findBySoldPriceIsNullAndExchangeEqualsAndAssetEquals(String exchange, String symbol);
  List<DbOrder> findBySoldPriceIsNullAndExchangeEquals(String exchange);
}
