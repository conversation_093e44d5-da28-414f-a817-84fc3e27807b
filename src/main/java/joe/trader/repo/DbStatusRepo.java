package joe.trader.repo;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import joe.trader.entity.DbStatus;

public interface DbStatusRepo extends CrudRepository<DbStatus, Long> {
  DbStatus findById(long id);

  @Query("SELECT b.balance FROM DbStatus b ORDER BY b.id DESC LIMIT 1")
  BigDecimal getPreviousBalance();

  List<DbStatus> findAllByOrderByIdDesc();


}
