package joe.trader.entity;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Entity
@Table(name = "dbprice")
@Data
@Builder
@AllArgsConstructor
public class DbPrice {
  @Id
  @GeneratedValue(strategy= GenerationType.IDENTITY)
  private Long id;

  public DbPrice()
  {
    this.timestamp = new Date();
  }

  @Column(name="timestamp", nullable=false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date timestamp;

  @Column(name="asset")
  private String asset;

  @Column(name="price", nullable=false, precision = 15, scale = 8)
  private BigDecimal price;

  @Column(name="lowprice", precision = 15, scale = 8)
  private BigDecimal lowPrice;

  @Column(name="highprice", precision = 15, scale = 8)
  private BigDecimal highPrice;

  @Column(name="idDbStatus")
  private Long idDbStatus;

}
