package joe.trader.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import org.hibernate.proxy.HibernateProxy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Multimap;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import joe.trader.client.markets.EXCHANGE;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "dbstatus")
@Getter
@Setter
@ToString
public class DbStatus {

  @Id
  @GeneratedValue(strategy=GenerationType.IDENTITY)
  private Long id;

  public DbStatus(EXCHANGE exchange)
  {
    this.timestamp = new Date();
    this.exchange = exchange.name();
  }

  public DbStatus() {

  }

  @Column(name="timestamp", nullable=false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date timestamp;

  @Column(name="balance", nullable=false, precision = 15, scale = 8)
  private BigDecimal balance;

  @Column(name="assetdesc")
  private String assetDesc;

  @Column(name="exchange")
  private String exchange;

  @Transient
  @JsonIgnore
  private Multimap assetInfo;

  @Column(name="idDbOrder")
  private long idDbOrder;

  @Column(name="delta", precision = 15, scale = 8)
  private BigDecimal delta;

  public void setPreviousBalance(BigDecimal previousBalance) {
    this.delta = this.balance.subtract(previousBalance);
  }

  @Override
  public final boolean equals(Object o) {
    if (this == o) return true;
    if (o == null) return false;
    Class<?> oEffectiveClass = o instanceof HibernateProxy ? ((HibernateProxy) o).getHibernateLazyInitializer().getPersistentClass() : o.getClass();
    Class<?> thisEffectiveClass = this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass() : this.getClass();
    if (thisEffectiveClass != oEffectiveClass) return false;
    DbStatus dbStatus = (DbStatus) o;
    return getId() != null && Objects.equals(getId(), dbStatus.getId());
  }

  @Override
  public final int hashCode() {
    return this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass().hashCode() : getClass().hashCode();
  }
}
