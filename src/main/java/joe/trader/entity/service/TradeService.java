package joe.trader.entity.service;

import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.repo.DbOrderRepo;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class TradeService {
  private final DbOrderRepo dbOrderRepo;

  @Transactional
  public void deleteAllOrders(EXCHANGE exchange) {
    dbOrderRepo.deleteAllByExchangeEquals(exchange.name());
  }
}
