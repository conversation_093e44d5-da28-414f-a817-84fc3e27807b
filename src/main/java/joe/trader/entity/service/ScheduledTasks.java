package joe.trader.entity.service;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import joe.trader.chat.EchoBot;
import joe.trader.client.JoeSpotClient;
import joe.trader.client.Trader;
import joe.trader.entity.service.math.MathService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service class that handles scheduled trading tasks for different cryptocurrencies.
 * Manages both buying and selling operations at configurable intervals.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduledTasks {

  private final Trader trader;
  private final EchoBot echoBot;
  private final JoeSpotClient joeSpotClient;

  @Value("${trader.buyer.fixed-rate:300000}")
  private int buyerFixedRate; // Default: 5 minutes

  @Value("${trader.seller.fixed-rate:120000}")
  private int sellerFixedRate; // Default: 2 minutes

  /**
   * Scheduled task that checks for buying opportunities for all enabled traders.
   * Runs at a fixed rate defined by buyerFixedRate property.
   */
  @Scheduled(fixedRateString = "${trader.buyer.fixed-rate:300000}", initialDelayString = "${trader.buyer.initial-delay:10000}")
  public void buyerTask() {
    log.debug("Starting buyer task at {}", LocalDateTime.now());

    if (!joeSpotClient.isInit()) {
      joeSpotClient.init();
      log.info("JoeSpotClient not initialized, initializing now");
      return;
    }
    trader.buyerTask(new MathService().rotateSymbol());
  }

  /**
   * Scheduled task that checks for selling opportunities for all enabled traders.
   * Runs at a fixed rate defined by sellerFixedRate property.
   */
  @Scheduled(fixedRateString = "${trader.seller.fixed-rate:120000}")
  public void sellerTask() {
    log.debug("Starting seller task at {}", LocalDateTime.now());

    if (!joeSpotClient.isInit()) {
      log.debug("JoeSpotClient not initialized, skipping seller task");
      return;
    }

    trader.sellerTask();
  }

  @CacheEvict(cacheNames = {"isSuperBullish", "isSuperBearish", "getSUPERTrend"})
  @Scheduled(fixedRate = 60*60*1000)
  public void cacheClear() {
    log.debug("Clearing cache");
  }


}