package joe.trader.entity.service.math;

import java.util.Arrays;

import joe.trader.client.symbol.SYMBOL;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MathService {

  private double y[];
  private double x[];

  @Getter
  private double[] coefs;

  private SYMBOL[] symbolRotate = SYMBOL.rotateValues();

  private MathService(double[] values) {
    this.y = values;
    this.x = new double[values.length];
    for (int i = 0; i < values.length; i++) {
      this.x[i] = i+1;
    }
  }

  public MathService() {

  }

  public static void main(String[] args) {
    System.out.println("Hello World!");

//    double[] values = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
//    double[] values = {-50,-70,-78,-50,-10,-5,5,30,20,40,50};
    double[] values = {1,6,17,34,57,86,121,162,209,262,321};
    double[] values2 = {-50,-70,-78,-50,-10,-5,5,30,20,40,50};
    double[] values3 = {50,70,80,70,50,40,10,5,5,0,-10};
    MathService mathService = new MathService(values);
    mathService.calculate(2);
    mathService.printCoefs();
    mathService.regenerate();
    System.out.println("isCurveUp: " + isCurveUp(values));
    System.out.println("isCurveDown: " + isCurveDown(values));
    System.out.println("isCurveUp: " + isCurveUp(values2));
    System.out.println("isCurveDown: " + isCurveDown(values2));
    System.out.println("isCurveUp: " + isCurveUp(values3));
    System.out.println("isCurveDown: " + isCurveDown(values3));

    SYMBOL symbol1 = mathService.rotateSymbol();
    SYMBOL symbol2 = mathService.rotateSymbol();
    SYMBOL symbol3 = mathService.rotateSymbol();
    SYMBOL symbol4 = mathService.rotateSymbol();
    SYMBOL symbol5 = mathService.rotateSymbol();
    SYMBOL symbol6 = mathService.rotateSymbol();
    SYMBOL symbol7 = mathService.rotateSymbol();
    SYMBOL symbol8 = mathService.rotateSymbol();
    SYMBOL symbol9 = mathService.rotateSymbol();
    SYMBOL symbol10 = mathService.rotateSymbol();

    System.out.println("Symbols: " + symbol1 + " " + symbol2 + " " + symbol3 + " " + symbol4 + " " + symbol5 + " " + symbol6 + " " + symbol7 + " " + symbol8 + " " + symbol9 + " " + symbol10);
  }

  public static boolean isCurveUp(double[] values) {
    MathService mathService = new MathService(values);
    mathService.calculate(2);
    return mathService.coefs[2] > 0;
  }

  public static boolean isCurveUpAndLastPositive(double[] values) {
    MathService mathService = new MathService(values);
    mathService.calculate(2);
    return mathService.coefs[2] > 0 && values[values.length-1]> 0;
  }

  public static boolean isCurveUpAndLastAbove(double[] values, double above) {
    MathService mathService = new MathService(values);
    mathService.calculate(2);
    return mathService.coefs[2] > 0 && values[values.length-1]> above;
  }

  public static boolean isCurveDown(double[] values) {
    MathService mathService = new MathService(values);
    mathService.calculate(2);
    return mathService.coefs[2] < 0;
  }

  public static boolean isLinearDown(double[] values) {
    MathService mathService = new MathService(values);
    mathService.calculate(1);
    return mathService.coefs[1] < 0;
  }

  public static boolean isLinearUp(double[] values) {
    MathService mathService = new MathService(values);
    mathService.calculate(1);
    return mathService.coefs[1] > 0;
  }

  public static boolean isLastLowerThanFirst(double [] values) {
    return values[values.length-1] < values[0];
  }

  public static boolean isLastGreaterThanFirst(double [] values) {
    return values[values.length-1] > values[0];
  }

  public SYMBOL rotateSymbol() {
    int randomNumber = Math.toIntExact(Math.round(Math.random() * (symbolRotate.length - 1)));
    log.debug("{} Symbols loaded, rotating to {}, number {}", symbolRotate.length, symbolRotate[randomNumber], randomNumber);
    return symbolRotate[randomNumber];
  }

  private void regenerate() {
    double[] xnew = new double[x.length];
    for (int i = 0; i < x.length; i++) {
      double value = 0;
      for (int j = 0; j < coefs.length; j++) {
        value += coefs[j] * Math.pow(x[i], j);
      }
      xnew[i] = value;
    }
    System.out.println("xnew: " + Arrays.toString(xnew));
  }

  public void calculate(int degree) {

    int numCoefficients = degree + 1;
    int numDataPoints = x.length;

    double[][] designMatrix = new double[numCoefficients][numCoefficients];
    double[] responseVector = new double[numCoefficients];

    // Fill the matrix A and vector B
    for (int i = 0; i < numCoefficients; i++) {
      for (int j = 0; j < numCoefficients; j++) {
        designMatrix[i][j] = 0;
        for (int k = 0; k < numDataPoints; k++) {
          designMatrix[i][j] += Math.pow(x[k], i + j);
        }
      }

      responseVector[i] = 0;
      for (int k = 0; k < numDataPoints; k++) {
        responseVector[i] += y[k] * Math.pow(x[k], i);
      }
    }

    coefs = gaussianElimination(designMatrix, responseVector);

  }

  // Solves the linear system Ax = B using Gaussian elimination
  public double[] gaussianElimination(double[][] matrixA, double[] vectorB) {

    int n = vectorB.length;

    for (int p = 0; p < n; p++) {
      int max = p;
      for (int i = p + 1; i < n; i++) {
        if (Math.abs(matrixA[i][p]) > Math.abs(matrixA[max][p])) {
          max = i;
        }
      }

      double[] tempRow = matrixA[p];
      matrixA[p] = matrixA[max];
      matrixA[max] = tempRow;

      double tempB = vectorB[p];
      vectorB[p] = vectorB[max];
      vectorB[max] = tempB;

      for (int i = p + 1; i < n; i++) {
        // Check for division by zero
        if (matrixA[p][p] == 0) {
          throw new ArithmeticException("Division by zero encountered during Gaussian elimination. Matrix is singular or ill-conditioned.");
        }

        double alpha = matrixA[i][p] / matrixA[p][p];
        vectorB[i] -= alpha * vectorB[p];
        for (int j = p; j < n; j++) {
          matrixA[i][j] -= alpha * matrixA[p][j];
        }
      }
    }

    double[] x = new double[n];
    for (int i = n - 1; i >= 0; i--) {
      double sum = 0.0;
      for (int j = i + 1; j < n; j++) { // Back-substitution
        sum += matrixA[i][j] * x[j];
      }

      // Check for division by zero
      if (matrixA[i][i] == 0) {
        throw new ArithmeticException("Division by zero encountered during back substitution. Matrix is singular or ill-conditioned.");
      }
      x[i] = (vectorB[i] - sum) / matrixA[i][i];
    }

    return x;
  }

  public void printCoefs() {
    for (int i = 0; i < coefs.length; i++) {
      System.out.println("Coef " + i + ": " + coefs[i]);
    }
  }

}