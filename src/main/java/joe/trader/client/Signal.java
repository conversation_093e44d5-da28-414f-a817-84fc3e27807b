package joe.trader.client;

import java.io.IOException;
import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public interface Signal {

   Logger log = LoggerFactory.getLogger(Signal.class);

   OkHttpClient client = new OkHttpClient();
   boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender);
   boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender);

   static String get(String url) throws IOException {
      return get(false, url);
   }

   static String get(boolean logRequest, String url) throws IOException {
      Request request = new Request.Builder()
          .url(url)
          .build();

      if(logRequest) {
        log.debug("----- GET {}", url);
      }

      try (Response response = client.newCall(request).execute()) {
        return response.body().string();
      }
   }
}
