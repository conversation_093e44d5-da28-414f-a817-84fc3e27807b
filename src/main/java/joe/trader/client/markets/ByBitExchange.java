package joe.trader.client.markets;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.bybit.api.client.domain.CategoryType;
import com.bybit.api.client.domain.account.AccountType;
import com.bybit.api.client.domain.asset.request.AssetDataRequest;
import com.bybit.api.client.domain.market.request.MarketDataRequest;
import com.bybit.api.client.impl.BybitApiAccountRestClientImpl;
import com.bybit.api.client.impl.BybitApiAssetRestClientImpl;
import com.bybit.api.client.impl.BybitApiMarketRestClientImpl;
import com.bybit.api.client.impl.BybitApiTradeAsyncRestClientImpl;
import com.bybit.api.client.service.BybitApiClientFactory;

import joe.trader.chat.EchoBot;
import joe.trader.client.Trader;
import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.markets.strategy.SUPERTrend;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbPrice;
import joe.trader.entity.DbStatus;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class ByBitExchange extends ExchangeDef {

  private final EXCHANGE exchange = EXCHANGE.BYBIT;

  private final ApplicationContext context;

  @Getter
  private BybitApiTradeAsyncRestClientImpl bybitClient;

  @Getter
  private BybitApiAccountRestClientImpl bybitAccountClient;

  @Getter
  private BybitApiAssetRestClientImpl bybitAssetClient;

  @Getter
  private BybitApiMarketRestClientImpl bybitMarketClient;

  @Value("${bybit.api.key}")
  private String bybitApiKey;

  @Value("${bybit.api.secret}")
  private String bybitSecretKey;

  @Value("${joe.params.usdAvailable:10.00}")
  private BigDecimal usdAvailable;

  @Autowired
  private SUPERTrend superTrend;

  private final EchoBot echoBot;

  public void init() {
    bybitClient = (BybitApiTradeAsyncRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAsyncTradeRestClient();
    bybitAccountClient = (BybitApiAccountRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAccountRestClient();
    bybitAssetClient = (BybitApiAssetRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAssetRestClient();
    bybitMarketClient = (BybitApiMarketRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newMarketDataRestClient();

    initialized = true;
    log.info("Bybit API client initialized successfully");
    echoBot.sendMessage("Bybit API client initialized successfully");
  }

  @Override
  public AssetBuyDTO checkBuyOrder(SYMBOL symbol) {

    if (!isUseThisExchange()) {
      log.debug("Not using bybit.");
      return null;
    }
    if (symbol == null) {
      log.debug("Symbol is null");
      return null;
    }
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }
    if (openOrder(exchange) != null && !openOrder(exchange).isFutures()) {
      log.info(">>> Already have an open order for [{}]", openOrder(exchange).getAsset());
      return null;
    }

    InfoAppender infoAppender = new InfoAppender(symbol, EXCHANGE.BYBIT);
    DbStatus dbStatus = new DbStatus(exchange);
    BigDecimal latestBalance = getLatestBalance();
    BigDecimal previousBalance = (BigDecimal) util.ifNull(dbStatusRepo.getPreviousBalance(), latestBalance);
    DbPrice dbPrice = getAssetPrice(symbol);
    priceSignal.setDbPrice(dbPrice);

    BigDecimal currentPrice = dbPrice.getPrice();
    infoAppender.append("Current Price", currentPrice.toPlainString());

    //Persist
    dbStatus.setBalance(latestBalance);
    dbStatus.setPreviousBalance(previousBalance);
    dbStatusRepo.save(dbStatus);

    String buyReason = "";
    boolean rsiBuy = rsiSignal.isOkToBuy(symbol, exchange, currentPrice, infoAppender);
//    boolean maBuy = maSignal.isOkToBuy(symbol, exchange, currentPrice, infoAppender);
    boolean macdBuy = macdSignal.isOkToBuy(symbol, exchange, currentPrice, infoAppender);
    boolean priceBuy = priceSignal.isOkToBuy(symbol, exchange, currentPrice, infoAppender);
    boolean isUptrend = rsiSignal.isUptrend(symbol, exchange, infoAppender); // This one is already at the end
    boolean sarBuy = sarSignal.isOkToBuy(symbol, exchange, currentPrice, infoAppender);
//    boolean squeezeBuy = squeezeSignal.is/**/OkToBuy(symbol, exchange, currentPrice);
    boolean superBullish = superTrend.isSuperBullish(symbol, exchange, infoAppender);
    boolean superBearish = superTrend.isSuperBearish(symbol, exchange, infoAppender);

    log.info("Is symbol {} superbullish? {} superbearish? {}", symbol, superBullish, superBearish);

    boolean buyReason1 = superBullish && (rsiBuy||isUptrend) && macdBuy && priceBuy && sarBuy;
    log.info(">>>>> SYMBOL [{}] superBullish && (rsiBuy||isUptrend) && macdBuy && priceBuy && sarBuy", symbol);
    log.info(">>>>> SYMBOL [{}] {} && ({}||{}) && {} && {} && {}",
        symbol, superBullish, rsiBuy, isUptrend, macdBuy, priceBuy, sarBuy);
    log.debug(">>>>> Always Buy: {}", isAlwaysBuy());
    log.debug(">>>>> Previous Balance: {} | Latest Balance: {} | UsdAvailable: {}", previousBalance, latestBalance, usdAvailable);
    if(buyReason1) buyReason = "Signals";
    boolean buyReason2 = alwaysBuy;
    if(buyReason2) buyReason = "Always Buy";

    final boolean buy = buyReason1 || buyReason2;

    if (buy) {
      log.info("===== Buying [{}] this time." , symbol);
      Trader trader = context.getBean(Trader.class);
      trader.buy(symbol, currentPrice, usdAvailable, buyReason);
    } else {
      log.info("===== Not buying [{}] this time.", symbol);
    }

    return AssetBuyDTO.builder().asset(symbol.getName())
        .rsiBuy(rsiBuy)
        .macdBuy(macdBuy)
        .priceBuy(priceBuy)
        .sarBuy(sarBuy)
        .uptrend(isUptrend)
        .alwaysBuy(isAlwaysBuy())
        .buy(buy)
        .buyReason("FLAGS RSI: %s MACD: %s SAR: %s PRICE: %s UPTREND: %s ALWAYSBUY: %s".formatted(rsiBuy, macdBuy, sarBuy, priceBuy, isUptrend, isAlwaysBuy()))
        .balance(latestBalance)
        .price(currentPrice)
        .lowPrice(dbPrice.getLowPrice())
        .highPrice(dbPrice.getHighPrice())
        .exchange(exchange.name())
        .dryOrders(dryOrders)
        .usdAvailable(usdAvailable)
        .build();
  }

  @Override
  public AssetSellDTO checkSellOrder() {
    DbOrder openOrder = openOrder(exchange);

    if (openOrder == null || openOrder.isFutures()) {
      log.debug(">>> No open order to sell");
      return null;
    }

    InfoAppender infoAppender = new InfoAppender(SYMBOL.fromName(openOrder.getAsset()), EXCHANGE.BYBIT);
    BigDecimal maxLossUSD = usdAvailable.multiply(new BigDecimal("0.005"));
    BigDecimal takeProfitUSD = usdAvailable.multiply(new BigDecimal("0.01"));
    BigDecimal maxToleranceAfterMaxUSD = usdAvailable.multiply(new BigDecimal("0.0025"));

    SYMBOL symbol = SYMBOL.fromName(openOrder.getAsset());
    int sellChecks = Optional.ofNullable(openOrder.getSellChecks()).orElse(0) + 1;
    log.debug("CheckSellOrder : {} sellChecks: {}", openOrder, sellChecks);

    DbPrice dbPrice = getAssetPrice(symbol);
    BigDecimal currentPrice = dbPrice.getPrice();
    boolean sameOrHigher = currentPrice.compareTo(openOrder.getPrice()) >= 0;
    BigDecimal deltaPrice = currentPrice.subtract(openOrder.getPrice()).setScale(8, RoundingMode.HALF_UP);
    BigDecimal deltaUSD = (currentPrice.multiply(openOrder.getQuantity()).setScale(8, RoundingMode.HALF_UP)).subtract(openOrder.getUsdValue());
    BigDecimal deltaUSDAbs = deltaUSD.abs();
    boolean isPositiveDelta = deltaUSD.compareTo(BigDecimal.ZERO) >= 0;

    BigDecimal maxValue = Optional.ofNullable(openOrder.getMaxValue()).orElse(openOrder.getPrice());

    if(!sameOrHigher)
    {
      openOrder.setConsecutiveLowerPriceCount(Optional.ofNullable(openOrder.getConsecutiveLowerPriceCount()).orElse(0) + 1);
      openOrder.setConsecutiveUpperPriceCount(0);
      log.info("Marking lower than buy price, times: {}. Sell Checks: {}", openOrder.getConsecutiveLowerPriceCount(), sellChecks);
    } else {
      openOrder.setConsecutiveLowerPriceCount(0);
      openOrder.setConsecutiveUpperPriceCount(Optional.ofNullable(openOrder.getConsecutiveUpperPriceCount()).orElse(0)+ 1);
      log.info("Marking same or higher than buy price, times: {}. Sell Checks: {}", openOrder.getConsecutiveUpperPriceCount(), sellChecks);
    }

    boolean isNewMax = currentPrice.compareTo(maxValue) > 0;
    if(isNewMax)
    {
      openOrder.setMaxValue(currentPrice);
      log.info("New max price: {} for {}, bought at {}, delta: {}", currentPrice, symbol, openOrder.getPrice(), deltaUSD);
    }
    boolean isLowerThanMax = currentPrice.compareTo(maxValue) < 0;

    int consecutiveLowerPriceCount = openOrder.getConsecutiveLowerPriceCount();
    int consecutiveUpperPriceCount = openOrder.getConsecutiveUpperPriceCount();

    long timePassed = System.currentTimeMillis()-openOrder.getTimestamp().getTime();
    int timePassedMin = (int) timePassed/1000/60;

    boolean rsiSell = rsiSignal.isOkToSell(symbol, exchange, currentPrice, infoAppender);
    boolean maSell = maSignal.isOkToSell(symbol, exchange, currentPrice, infoAppender);
    boolean macdSell = macdSignal.isOkToSell(symbol, exchange, currentPrice, infoAppender);
    boolean priceSell = priceSignal.isOkToSell(symbol, exchange, currentPrice, infoAppender);
    boolean isDowntrend = rsiSignal.isDowntrend(symbol, exchange, infoAppender);

    log.info(">>>>> {} RSI Sell: {} | MA Sell: {} | MACD Sell: {} | Price Sell: {} | RSI Downtrend: {} | Lower Price Count: {} | Upper Price Count: {}", symbol, rsiSell, maSell, macdSell, priceSell, isDowntrend, consecutiveLowerPriceCount, consecutiveUpperPriceCount);
    log.info(">>>>> {} Time Passed: {} minutes | Max Loss: {} | Take Profit: {} | Max Tolerance After Max: {} | Delta Price: {} | Delta USD: {}", symbol,
        timePassedMin, maxLossUSD, takeProfitUSD, maxToleranceAfterMaxUSD, deltaPrice, deltaUSD);

    String sellReason = "";
    boolean sellReason1 = isDowntrend && (rsiSell || maSell || macdSell || priceSell);
    if (sellReason1) sellReason = "Downtrend";
    boolean sellReason2 = isAlwaysSell();
    if (sellReason2) sellReason = "Always Sell";
    boolean sellReason7 = maxLossUSD.compareTo(deltaUSDAbs)<0&&!isPositiveDelta;
    if (sellReason7) sellReason = "Max Loss " + maxLossUSD;
    boolean sellReason6 = sellChecks > 180;
    if (sellReason6) sellReason = "Sell Checks > 180";
    boolean sellReason8 = takeProfitUSD.compareTo(deltaUSDAbs)<0&&isPositiveDelta;
    if (sellReason8) sellReason = "Take Profit " + takeProfitUSD;
    boolean sellReason9 = sameOrHigher && timePassedMin>=10;
    if (sellReason9) sellReason = "10 minutes take any profit: " + deltaUSD;
    boolean sellReason10 = timePassedMin>=60;
    if (sellReason10) sellReason = "60 minutes sell no matter what";
    boolean sellReason11 = consecutiveLowerPriceCount >= 3 && deltaUSDAbs.compareTo(maxToleranceAfterMaxUSD)>0;
    if (sellReason11) sellReason = "Consecutive Lower Price Count >= 3";
    boolean sellReason12 = isLowerThanMax&&maxToleranceAfterMaxUSD.compareTo(deltaUSDAbs)<0&&isPositiveDelta;
    if (sellReason12) sellReason = "Lower than Max, but winning";
    boolean sellReason13 = consecutiveUpperPriceCount >= 5 && isPositiveDelta;
    if (sellReason13) sellReason = "No greedy, Consecutive Upper Price Count >= 5";
    boolean sellReason14 = isNewMax && sellChecks>=3;
    if (sellReason14) sellReason = "Take any profit after 3 min";

    openOrder.setSellChecks(sellChecks);
    openOrder.setDeltaUSD(deltaUSD);
    openOrder.setConsecutiveLowerPriceCount(consecutiveLowerPriceCount);
    openOrder.setConsecutiveUpperPriceCount(consecutiveUpperPriceCount);

    final boolean sell = sellReason1 || sellReason2 || sellReason9 || sellReason6 || sellReason7 || sellReason8 || sellReason10 || sellReason11 || sellReason12 || sellReason13 || sellReason14;

    if (sell) {
      log.info("===== Selling this time. Reason: {}", sellReason);
      Trader trader = context.getBean(Trader.class);
      trader.sell(symbol, openOrder, currentPrice, sellReason);
    } else {
      log.info("===== Not selling this time. Current Price: {}, Bought Price: {}, Max Price {}, DeltaUSD: {}, Purchased {} minutes ago", currentPrice, openOrder.getPrice(), maxValue, deltaUSD, timePassed/1000/60);
      openOrder.setLatestPrice(currentPrice);
      dbOrderRepo.save(openOrder);
    }

    return AssetSellDTO.builder().asset(symbol.getName())
        .rsiSell(rsiSell)
        .maSell(maSell)
        .macdSell(macdSell)
        .priceSell(priceSell)
        .downtrend(isDowntrend)
        .alwaysSell(isAlwaysSell())
        .sell(sell)
        .sellReason(sellReason)
        .dryOrders(dryOrders)
        .exchange(exchange.name())
        .balance(getLatestBalance())
        .price(currentPrice)
        .lowPrice(dbPrice.getLowPrice())
        .highPrice(dbPrice.getHighPrice())
        .build();
  }

  @Override
  public BigDecimal getLatestBalance() {
    BigDecimal walletBalance;
    try {
      final var info = bybitAssetClient.getAssetAllCoinsBalance(AssetDataRequest.builder().accountType(AccountType.UNIFIED).coin("USDT").build());
      walletBalance = getFromResults(info, "result", "balance", "WalletBalance", "walletBalance");
    } catch (Exception e) {
      log.error("Error retrieving balance: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve balance", e);
    }
    log.info("Wallet balance: {} USD Available for trades: {}", walletBalance, usdAvailable);
    Trader trader = context.getBean(Trader.class);
    trader.setWalletBalance(walletBalance);
    return walletBalance;
  }

  @Override
  public DbPrice getAssetPrice(SYMBOL symbol) {

    if ("USDC".equals(symbol.getName()) || "USDT".equals(symbol.getName())) {
      return DbPrice.builder().price(BigDecimal.ONE).lowPrice(BigDecimal.ONE).highPrice(BigDecimal.ONE).build();
    }

    final var info = bybitMarketClient.getMarketTickers(MarketDataRequest.builder()
        .symbol(symbol.getPair2())
        .category(CategoryType.SPOT)
        .build());

    BigDecimal price = getFromResults(info, "result", "list", "lastPrice");
    BigDecimal lowPrice = getFromResults(info, "result", "list", "lowPrice24h");
    BigDecimal highPrice = getFromResults(info, "result", "list", "highPrice24h");

    log.debug("Current price for {}: {}", symbol, price);
    log.debug("Low price for {}: {}", symbol, lowPrice);
    log.debug("High price for {}: {}", symbol, highPrice);

    return util.logR(DbPrice.builder()
        .asset(symbol.getName())
        .price(price)
        .lowPrice(lowPrice)
        .highPrice(highPrice)
        .timestamp(new Date())
        .build());
  }

  private BigDecimal getFromResults(Object info, String... path) {
    Map<?, ?> map = (Map<?, ?>) info;
    List<?> list;
    for (String pathPart : path) {
      Object inside1 = map.get(pathPart);
      if (inside1 instanceof List<?>) {
        list = (List<?>) inside1;
        map = (Map<?, ?>) list.getFirst();
        break;
      }
      map = (Map<?, ?>) inside1;
    }
    return new BigDecimal(map.get(path[path.length - 1]).toString());
  }
}
