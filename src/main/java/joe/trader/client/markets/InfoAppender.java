package joe.trader.client.markets;

import java.util.ArrayList;
import java.util.List;

import joe.trader.client.symbol.SYMBOL;
import lombok.Data;

@Data
public class InfoAppender {

  private List<String> info = new ArrayList<>();

  private final SYMBOL symbol;
  private final EXCHANGE exchange;
  private final long timestamp;

  public InfoAppender(SYMBOL symbol, EXCHANGE exchange) {
    this.symbol = symbol;
    this.exchange = exchange;
    this.timestamp = System.currentTimeMillis();
  }

  public void append(String label, String message) {
    info.add(label + "=" + message);
  }

  public String getInfo() {
    return String.join(" | ", info);
  }

}
