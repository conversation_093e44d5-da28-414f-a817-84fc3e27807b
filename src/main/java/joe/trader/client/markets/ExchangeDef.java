package joe.trader.client.markets;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;

import joe.trader.chat.EchoBot;
import joe.trader.client.Trader;
import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.signals.MACDSignal;
import joe.trader.client.signals.MASignal;
import joe.trader.client.signals.PriceSignal;
import joe.trader.client.signals.RSISignal;
import joe.trader.client.signals.SARSignal;
import joe.trader.client.signals.SQUEZZESignal;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbPrice;
import joe.trader.repo.DbOrderRepo;
import joe.trader.repo.DbPriceRepo;
import joe.trader.repo.DbStatusRepo;
import joe.trader.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class ExchangeDef {

  @Autowired
  private ApplicationContext context;

  @Value("${joe.params.alwaysBuy}")
  @Getter
  protected boolean alwaysBuy;

  @Value("${joe.params.alwaysSell}")
  @Getter
  protected boolean alwaysSell;

  @Value("${joe.use-binance:false}")
  private boolean useBinance;

  @Value("${joe.use-bybit:false}")
  private boolean useBybit;

  @Value("${joe.params.dryOrders}")
  @Getter
  protected boolean dryOrders;

  @Getter
  private Set<String> assetList;

  private Map<SYMBOL, Trader> traderMap;

  protected Util util;
  protected DbOrderRepo dbOrderRepo;
  protected DbStatusRepo dbStatusRepo;
  protected DbPriceRepo dbPriceRepo;
  protected RSISignal rsiSignal;
  protected MASignal maSignal;
  protected MACDSignal macdSignal;
  protected PriceSignal priceSignal;
  protected SARSignal sarSignal;
  protected SQUEZZESignal squeezeSignal;
  protected EchoBot echoBot;

  protected boolean initialized = false;

  public abstract void init();
  public abstract AssetBuyDTO checkBuyOrder(SYMBOL symbol);
  public abstract AssetSellDTO checkSellOrder();
  public abstract BigDecimal getLatestBalance();
  public abstract DbPrice getAssetPrice(SYMBOL symbol);


  public final boolean isInit(){
    return initialized;
  }

  public DbOrder openOrder(EXCHANGE exchange) {
    List<DbOrder> openOrders = dbOrderRepo.findBySoldPriceIsNullAndExchangeEquals(exchange.name());
    if (openOrders.isEmpty()) {
      return null;
    }
    SYMBOL symbol = SYMBOL.fromName(openOrders.get(0).getAsset());
    log.debug("Open {} Orders: {}", symbol, openOrders.size());
    return openOrders.isEmpty() ? null : openOrders.getFirst();
  }

  public boolean isUseThisExchange() {
    return (useBybit && this instanceof ByBitExchange);
  }


  @Autowired
  public final void setUtil(Util util) {
    this.util = util;
  }
  @Autowired
  public final void setEchoBot(EchoBot echoBot) {
    this.echoBot = echoBot;
  }
  @Autowired
  public final void setDbOrderRepo(DbOrderRepo dbOrderRepo) {
    this.dbOrderRepo = dbOrderRepo;
  }
  @Autowired
  public final void setDbStatusRepo(DbStatusRepo dbStatusRepo) {
    this.dbStatusRepo = dbStatusRepo;
  }
  @Autowired
  public final void setDbPriceRepo(DbPriceRepo dbPriceRepo) {
    this.dbPriceRepo = dbPriceRepo;
  }
  @Autowired
  public final void setRsiSignal(RSISignal rsiSignal) {
    this.rsiSignal = rsiSignal;
  }
  @Autowired
  public final void setMaSignal(MASignal maSignal) {
    this.maSignal = maSignal;
  }
  @Autowired
  public final void setMacdSignal(MACDSignal macdSignal) {
    this.macdSignal = macdSignal;
  }
  @Autowired
  public final void setPriceSignal(PriceSignal priceSignal) {
    this.priceSignal = priceSignal;
  }
  @Autowired
  public final void setSarSignal(SARSignal sarSignal) {
    this.sarSignal = sarSignal;
  }
  @Autowired
  public final void setSqueezeSignal(SQUEZZESignal squeezeSignal) {
    this.squeezeSignal = squeezeSignal;
  }
}
