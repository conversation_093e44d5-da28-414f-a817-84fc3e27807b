package joe.trader.client.markets.strategy;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.service.math.MathService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@RequiredArgsConstructor
public class SUPERTrend {

  final SUPERTrendInfo supertrendInfo;

  @Bean
  public SUPERTrendInfo supertrendInfo() {
    return new SUPERTrendInfo();
  }

  @SneakyThrows
  @Cacheable(value = "isSuperBullish", key = "#symbol.name() + #exchange.name()")
  public boolean isSuperBullish(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) {
    double[] info = supertrendInfo.getInfo(symbol, exchange, infoAppender);
    return MathService.isLinearUp(info)&&MathService.isLastGreaterThanFirst(info);
  }

  @SneakyThrows
  @Cacheable(value = "isSuperBearish", key = "#symbol.name() + #exchange.name()")
  public boolean isSuperBearish(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) {
    double[] info = supertrendInfo.getInfo(symbol, exchange, infoAppender);
    return MathService.isLinearDown(info)&&MathService.isLastLowerThanFirst(info);
  }

}