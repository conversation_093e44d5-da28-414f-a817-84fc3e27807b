package joe.trader.client.markets.strategy;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SUPERTrendInfo {

  private final int results = 24;//4hours
  private final String interval = "15m";
  private final int last = results - 1;

  @Value("${joe.taapi.io.key}")
  private String taapiiokey;

  @Cacheable(value = "getSUPERTrend", key = "#symbol.name() + #exchange.name()")
  public double[] getInfo(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) throws IOException {

    log.info("Getting SUPERTrend information for symbol {} on exchange {}, interval {} results {}", symbol.getName(), exchange.name(), interval, results);

    // Make API call to get klines
    String result = Signal.get(true,"https://api.taapi.io/price?secret=" + taapiiokey + "&exchange=" + exchange.name().toLowerCase() + "&symbol=" + symbol.getName() + "USDT&interval=" + interval + "&results=" + results + "&addResultTimestamp=true");

    // Parse the response
    log.debug("PRICE {} {} information for symbol {} on exchange {}: {}", interval, results, symbol.getName(), exchange.name(), result);

    if (result.contains("error")) {
      throw new IOException("Failed to get Price information for symbol " + symbol.getName() + " on exchange " + exchange.name() + ": " + result);
    }

    double[] results = new double[this.results];

    try {

      JSONObject jsonObject = new JSONObject(result);
      List<Object> data = jsonObject.getJSONArray("value").toList();

      int i = 0;
      for (Object item : data) {
        results[i++] = ((BigDecimal) item).doubleValue();
      }
      log.debug("Current Price value for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), results[last]);
    } catch (Exception e) {
      log.error("Error parsing Price information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      throw new IOException("Failed to parse Price information for symbol " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
    return results;
  }
}
