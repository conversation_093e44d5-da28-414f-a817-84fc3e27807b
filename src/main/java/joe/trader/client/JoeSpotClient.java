package joe.trader.client;

import java.util.List;

import org.springframework.stereotype.Component;

import com.bybit.api.client.impl.BybitApiTradeAsyncRestClientImpl;

import joe.trader.client.markets.ByBitExchange;
import joe.trader.entity.DbStatus;
import joe.trader.repo.DbOrderRepo;
import joe.trader.repo.DbStatusRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Client for interacting with cryptocurrency exchange API.
 *
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JoeSpotClient {

  private final DbStatusRepo dbStatusRepo;
  private final DbOrderRepo dbOrderRepo;
//  private final BinanceExchange binanceMarket;
  private final ByBitExchange bybitMarket;

  public boolean isInit()
  {
    log.debug("isInit: Binance {} Bybit {}", bybitMarket.isInit());
    return  bybitMarket.isInit();
  }

  /**
   * Initializes the spot client with trading parameters
   */
  public void init() {
    try {
      if (bybitMarket.isUseThisExchange()) {
        log.debug("Initializing Bybit API client");
        bybitMarket.init();
        log.debug("Bybit API client initialized successfully");
      }

      log.debug("Spot client initialized successfully");
    } catch (Exception e) {
      log.error("Failed to initialize client: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to initialize client", e);
    }
  }

  /**
   * Retrieves all database status records ordered by ID in descending order
   *
   * @return List of DbStatus objects
   */
  public List<DbStatus> getDbStatus() {
    try {
      List<DbStatus> statusList = dbStatusRepo.findAllByOrderByIdDesc();
      log.debug("Retrieved {} status records", statusList.size());
      return statusList;
    } catch (Exception e) {
      log.error("Error retrieving status records: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve status records", e);
    }
  }

  /**
   * Deletes all database status records
   *
   * @throws RuntimeException if there's an error deleting the records
   */
  public void deleteAllDbStatus() {
    try {
      dbStatusRepo.deleteAll();
      log.info("All status records deleted successfully");
    } catch (Exception e) {
      log.error("Error deleting status records: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to delete status records", e);
    }
  }

  /**
   * Gets the deltas (profit/loss) for all orders
   *
   * @return String representation of the deltas
   */
  public String getDBDeltas() {
    try {
      String deltas = dbOrderRepo.getDBDeltas().toString();
      log.debug("Retrieved order deltas: {}", deltas);
      return deltas;
    } catch (Exception e) {
      log.error("Error retrieving order deltas: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve order deltas", e);
    }
  }

  public BybitApiTradeAsyncRestClientImpl getBybitClient() {
    return bybitMarket.getBybitClient();
  }
}
