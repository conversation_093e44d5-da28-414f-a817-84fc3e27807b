package joe.trader.client.signals;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.service.math.MathService;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class MACDSignal implements Signal {

  @Value("${joe.taapi.io.key}")
  private String taapiiokey;

  boolean useSimpleMADC = true;
  int results = 10;
  int last = results-1;


  @Override
  public boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    try {
      double[] macd = getInfo(symbol, exchange, infoAppender, results);

      if(useSimpleMADC) {
        return macd[last] > 0 && macd[last-1] > macd[last-2] && macd[last-2] > macd[last-3];
      } else {
        if(isReversalToUptrend(macd)) {
          log.info(">MACD uptrend Reversal for {} detected.", symbol) ;
          return true;
        }
      }
      return false;
    } catch (IOException e) {
      log.error("Error getting MACD information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      return false;
    }
  }

  private boolean isReversalToUptrend(double[] macd) {
    return MathService.isCurveUpAndLastPositive(macd);
  }

  private boolean isReversalToDowntrend(double[] macd) {
    return MathService.isCurveDown(macd);
  }

  @Override
  public boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    try {
      double[] macd = getInfo(symbol, exchange, infoAppender, 10);
      if(useSimpleMADC) {
        return macd[last] < macd[last-1] && macd[last-1] < macd[last-2] && macd[last-2] < macd[last-3];
      } else {
        log.info(">MACD downtrend reversal for {} detected", symbol);
        return isReversalToDowntrend(macd);
      }
    } catch (IOException e) {
      log.error("Error getting MACD information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      return false;
    }
  }

  public double[] getInfo(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender, int results) throws IOException {

    String result = Signal.get("https://api.taapi.io/macd?secret=" + taapiiokey + "&exchange=" + exchange.name().toLowerCase() + "&symbol=" + symbol.getName() + "USDT&interval=1m&results=" + results + "&addResultTimestamp=true");

    // Parse the response
    log.debug("MACD information for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), result);

    if (result.contains("error")) {
      throw new IOException("Failed to get MACD information for symbol " + symbol.getName() + " on exchange " + exchange.name() + ": " + result);
    }

    JSONObject jsonObject = new JSONObject(result);
    List<Object> data = jsonObject.getJSONArray("valueMACDHist").toList();
    double[] ma = new double[results];
    int i = 0;
    for (Object item : data) {
      ma[i++] = ((BigDecimal) item).doubleValue();
    }
    log.debug("Current MACD value for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), ma[2]);
    return ma;
  }


}