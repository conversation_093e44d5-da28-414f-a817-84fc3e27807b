package joe.trader.client.signals;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.service.math.MathService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class RSISignal implements Signal {


  @Value("${joe.taapi.io.key}")
  private String taapiiokey;
  private final int results = 10;
  private final int last = results - 1;

  @Override
  public boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    try {
      double[] rsi = getInfo(symbol, exchange, infoAppender);
      //Check if the last 3 values are below 50
      int count = 0;
      for (int i = rsi.length - 3; i < rsi.length; i++) {
        if (rsi[i] < 50) {
          count++;
        }
      }
      return count == 3;
    } catch (Exception e) {
      log.error("Error checking RSI signal for asset {} on exchange {}: {}", symbol.getName(), exchange.name(), e.getMessage(), e);
      throw new RuntimeException("Failed to check RSI signal for asset " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
  }

  @Override
  public boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    try {
      double[] rsi = getInfo(symbol, exchange, infoAppender);
      //Check if the last 3 values are above 50
      int count = 0;
      for (int i = rsi.length - 3; i < rsi.length; i++) {
        if (rsi[i] > 50) {
          count++;
        }
      }
      return count == 3;
    } catch (Exception e) {
      log.error("Error checking RSI signal for asset {} on exchange {}: {}", symbol.getName(), exchange.name(), e.getMessage(), e);
      throw new RuntimeException("Failed to check RSI signal for asset " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
  }

  public boolean isUptrend(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) {
    try {
      double[] rsi = getInfo(symbol, exchange, infoAppender);
      return MathService.isCurveUpAndLastAbove(rsi, 50);
    } catch (Exception e) {
      log.error("Error checking RSI uptrend for asset {} on exchange {}: {}", symbol.getName(), exchange.name(), e.getMessage(), e);
      throw new RuntimeException("Failed to check RSI signal for asset " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
  }

  public boolean isDowntrend(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) {
    try {
      double[] rsi = getInfo(symbol, exchange, infoAppender);
      return MathService.isCurveDown(rsi);
    } catch (Exception e) {
      log.error("Error checking RSI downtrend for asset {} on exchange {}: {}", symbol.getName(), exchange.name(), e.getMessage(), e);
      throw new RuntimeException("Failed to check RSI signal for asset " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
  }

  public double[] getInfo(SYMBOL symbol, EXCHANGE exchange, InfoAppender infoAppender) throws IOException {
    // Make API call to get klines
    String result = Signal.get("https://api.taapi.io/rsi?secret=" + taapiiokey + "&exchange=" + exchange.name().toLowerCase() + "&symbol=" + symbol.getName() + "USDT&interval=1m&results=" + results);

    // Parse the response
    log.debug("RSI information for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), result);

    if (result.contains("error")) {
      throw new IOException("Failed to get RSI information for symbol " + symbol.getName() + " on exchange " + exchange.name() + ": " + result);
    }

    double[] rsi = new double[results];

    try {
      JSONObject jsonObject = new JSONObject(result);
      List<Object> data = jsonObject.getJSONArray("value").toList();

      int i = 0;
      for (Object item : data) {
        rsi[i++] = ((BigDecimal) item).doubleValue();
      }
      log.debug("Current RSI value for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), rsi[last]);
    } catch (Exception e) {
      log.error("Error parsing RSI information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      throw new IOException("Failed to parse RSI information for symbol " + symbol.getName() + " on exchange " + exchange.name(), e);
    }
    return rsi;
  }

}
