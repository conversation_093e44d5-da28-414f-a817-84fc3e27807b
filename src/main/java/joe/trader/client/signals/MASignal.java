package joe.trader.client.signals;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.markets.InfoAppender;
import joe.trader.client.symbol.SYMBOL;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class MASignal implements Signal {

  @Value("${joe.taapi.io.key}")
  private String taapiiokey;

  @Override
  public boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    log.debug("isOkToBuy for symbol {} on exchange {}: price {}", symbol.getName(), exchange.name(), price);
    try {
      return getInfo(symbol, exchange)[2] < price.doubleValue();
    } catch (IOException e) {
      log.error("Error getting MA information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      return false;
    }
  }

  @Override
  public boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price, InfoAppender infoAppender) {
    log.debug("isOkToSell for symbol {} on exchange {}: price {}", symbol.getName(), exchange.name(), price);
    try {
      return getInfo(symbol, exchange)[2] > price.doubleValue();
    } catch (IOException e) {
      log.error("Error getting MA information for symbol {} on exchange {}", symbol.getName(), exchange.name(), e);
      return false;
    }
  }

  public double[] getInfo(SYMBOL symbol, EXCHANGE exchange) throws IOException {
    String result = Signal.get("https://api.taapi.io/ma?secret=" + taapiiokey + "&exchange=" + exchange.name().toLowerCase() + "&symbol=" + symbol.getName() + "USDT&interval=1m&results=3");

    // Parse the response
    log.debug("MA information for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), result);

    if (result.contains("error")) {
      throw new IOException("Failed to get MA information for symbol " + symbol.getName() + " on exchange " + exchange.name() + ": " + result);
    }

    JSONObject jsonObject = new JSONObject(result);
    List<Object> data = jsonObject.getJSONArray("value").toList();
    double[] ma = new double[3];
    int i = 0;
    for (Object item : data) {
      ma[i++] = ((Number) item).doubleValue();
    }
    log.debug("Current MA value for symbol {} on exchange {}: {}", symbol.getName(), exchange.name(), ma[2]);
    return ma;
  }
}
