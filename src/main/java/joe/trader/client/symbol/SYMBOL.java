package joe.trader.client.symbol;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;

//  private String[] gainers = {"MEE,3,2", "LUNAI,6,1", "KASTA,6,1", "BLAST,5,2", "CRV,0,2", "APEX,4,2", "HMSTR,6,2", "FXS,3,2"};
@Getter
public enum SYMBOL {
  SOLUSDT  ("SOL"  , "SOL/USDT"  , "SOLUSDT"  , 2, 3),
  BNBUSDT  ("BNB"  , "BNB/USDT"  , "BNBUSDT"  , 1, 4),
  XRPUSDT  ("XRP"  , "XRP/USDT"  , "XRPUSDT"  , 4, 2),
  DOTUSDT  ("DOT"  , "DOT/USDT"  , "DOTUSDT"  , 3, 3),
  WALUSDT  ("WAL"  , "WAL/USDT"  , "WALUSDT"  , 4, 1, true),
  MNTUSDT  ("MNT"  , "MNT/USDT"  , "MNTUSDT"  , 4, 2),
  BTCUSDT  ("BTC"  , "BTC/USDT"  , "BTCUSDT"  , 1, 6, true),
  HUSDT    ("H"    , "HUSDT/USDT", "HUSDT"    , 6, 1),
  HYPEUSDT ("HYPE" , "HYPE/USDT" , "HYPEUSDT" , 2, 3),
  PENGUUSDT("PENGU", "PENGU/USDT", "PENGUUSDT", 6, 0),
  MEEUSDT  ("MEE"  , "MEE/USDT"  , "MEEUSDT"  , 3, 2, true),
  LUNAIUSDT("LUNAI", "LUNAI/USDT", "LUNAIUSDT", 6, 1),
  KASTAUSDT("KASTA", "KASTA/USDT", "KASTAUSDT", 6, 1),
  BLASTUSDT("BLAST", "BLAST/USDT", "BLASTUSDT", 5, 2),
  FXSUSDT  ("FXS"  , "FXS/USDT"  , "FXSUSDT"  , 3, 2),
  APEXUSDT ("APEX" , "APEX/USDT" , "APEXUSDT" , 4, 2),
  HMSTRUSDT("HMSTR", "HMSTR/USDT", "HMSTRUSDT", 6, 2),
  CRVUSDT  ("CRV"  , "CRV/USDT"  , "CRVUSDT"  , 0, 2, true),
  FLOKIUSDT("FLOKI", "FLOKI/USDT", "FLOKIUSDT", 8, 0),
  XAUUSDT  ("XAU"  , "XAU/USDT"  , "XAUUSDT"  , 1, 5, true),
  ERAUSDT  ("ERA"  , "ERA/USDT"  , "ERAUSDT"  , 4, 1, false),
  USDT     ("USDT" , "USDT/USDT" , "USDTUSDT");
  private final String name;
  private final String pair;
  private final String pair2;
  private int priceDecimals = 6;
  private int quantityDecimals = 3;
  private boolean disabled = false;

  @Getter
  private static SYMBOL singleSymbolMode = ERAUSDT;

  public static SYMBOL fromName(String symbol) {
    for (SYMBOL s : SYMBOL.values()) {
      if (s.getName().equals(symbol)) {
        return s;
      }
    }
    throw new IllegalArgumentException("No symbol found for " + symbol);
  }

  SYMBOL(String name, String pair, String pair2) {
    this.name = name;
    this.pair = pair;
    this.pair2 = pair2;
  }

  SYMBOL(String name, String pair, String pair2, int priceDecimals, int quantityDecimals) {
    this.name = name;
    this.pair = pair;
    this.pair2 = pair2;
    this.priceDecimals = priceDecimals;
    this.quantityDecimals = quantityDecimals;
  }

  SYMBOL(String name, String pair, String pair2, int priceDecimals, int quantityDecimals, boolean disabled) {
    this.name = name;
    this.pair = pair;
    this.pair2 = pair2;
    this.priceDecimals = priceDecimals;
    this.quantityDecimals = quantityDecimals;
    this.disabled = disabled;
  }

  public static SYMBOL[] rotateValues() {

    if (SYMBOL.getSingleSymbolMode() != null) {
      return new SYMBOL[]{SYMBOL.getSingleSymbolMode()};
    }

    List<SYMBOL> symbols = new ArrayList<>();
    for (SYMBOL s : SYMBOL.values()) {
      if(s != USDT && !s.isDisabled()){
        symbols.add(s);
      }
    }
    return symbols.toArray(new SYMBOL[0]);
  }

  public BigDecimal roundPrice(BigDecimal price) {
    return price.setScale(this.getPriceDecimals(), RoundingMode.HALF_UP);
  }
}
