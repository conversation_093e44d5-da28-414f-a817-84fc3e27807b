package joe.trader.client;

import java.awt.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.markets.ByBitExchange;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbOrder;
import joe.trader.repo.DbOrderRepo;
import joe.trader.util.Util;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class Trader {

//  private final BinanceExchange binanceMarket;
  private final ByBitExchange bybitMarket;
  private final DbOrderRepo dbOrderRepo;
  private final Util util;
  @Getter
  protected int minUSD = 10;
  boolean tpSlEnabled = false;
  boolean persistOrders = true;
  boolean enableFutures = true;
  final String margin = "10";

  @Value("${joe.params.dryOrders}")
  private boolean dryOrders;

  @Value("${joe.params.useMarketPrice}")
  private boolean useMarketPrice;

  @Getter
  @Setter
  private BigDecimal walletBalance;

  public DbOrder buy(SYMBOL symbol, BigDecimal price, BigDecimal usdValue, String notes) {

    // Calculate initial quantity
    BigDecimal quantity = usdValue.divide(price, 5, RoundingMode.HALF_UP);
    if (usdValue.compareTo(walletBalance)>0)
    {
      log.error("Not enough balance to buy");
      return null;
    }

    if (usdValue.compareTo(new BigDecimal(minUSD)) < 0) {
      log.error("Order too small");
      return null;
    }

    price = symbol.roundPrice(price);

    // Normalize quantity according to step size
    final BigDecimal normalizedQuantity = quantity.setScale(symbol.getQuantityDecimals(), RoundingMode.HALF_UP);
    BigDecimal takeProfitPrice = getTakeProfitPrice(symbol, price, normalizedQuantity, margin);
    BigDecimal stopLossPrice = getStopLossPrice(symbol, price, normalizedQuantity, margin);

    Map<String, Object> parameters = getBuyMap(symbol, price, normalizedQuantity, takeProfitPrice, stopLossPrice);

    if(enableFutures) {
      parameters.put("isLeverage", enableFutures);
      parameters.put("margin", margin);
    }

    BigDecimal currentValue = normalizedQuantity.multiply(price);

    log.info(">>> Creating buy order (asset {} dry={}) with price {} quantity {} currentUSDValue {}, futures {} margin {}x ",
        symbol, dryOrders, price, normalizedQuantity, currentValue, enableFutures, margin);
    DbOrder dbOrder = DbOrder.builder()
        .usdValue(currentValue)
        .price(price)
        .quantity(normalizedQuantity)
        .asset(symbol.getName())
        .side("BUY")
        .buyReason(notes)
        .takeProfit(takeProfitPrice)
        .stopLoss(stopLossPrice)
        .timestamp(new Date())
        .futures(enableFutures)
        .margin(margin + "X")
        .exchange(bybitMarket.isUseThisExchange() ? "BYBIT" : "BINANCE")
        .build();

    if (!dryOrders) {
      if (bybitMarket.isUseThisExchange()) {
        bybitMarket.getBybitClient().createOrder(parameters, System.out::println);
      }
//      if (binanceMarket.isUseThisExchange()) {
//        binanceMarket.getBinanceClient().createTrade().newOrder(parameters);
//      }
      Toolkit.getDefaultToolkit().beep();
      if (persistOrders) {
        dbOrderRepo.save(dbOrder);
      }
    }

    return dbOrder;
  }

  private BigDecimal getStopLossPrice(SYMBOL symbol, BigDecimal price, BigDecimal normalizedQuantity, String margin) {
    BigDecimal stopLossPrice = price.multiply(new BigDecimal("0.99"));
    return symbol.roundPrice(stopLossPrice);
  }

  private BigDecimal getTakeProfitPrice(SYMBOL symbol, BigDecimal price, BigDecimal normalizedQuantity, String margin) {

    BigDecimal takeProfitPrice = price.multiply(new BigDecimal("1.01"));
    return symbol.roundPrice(takeProfitPrice);
  }

  private Map<String, Object> getBuyMap(SYMBOL symbol, BigDecimal price, BigDecimal normalizedQuantity, BigDecimal takeProfitPrice, BigDecimal stopLossPrice) {

    Map<String, Object> parameters = new LinkedHashMap<>();
    parameters.put("symbol", symbol.getPair2());
//    parameters.put("recvWindow", 5000);
//    parameters.put("timestamp", System.currentTimeMillis());

    parameters.put("side", "BUY");
//    parameters.put("type", "LIMIT");
//    parameters.put("timeInForce", "GTC");

    if(enableFutures) {
      parameters.put("category", "linear");
    } else {
      parameters.put("category", "spot");
    }

//    parameters.put("quantity", normalizedQuantity);
    parameters.put("price", price.setScale(symbol.getPriceDecimals(), RoundingMode.HALF_UP));

    //bybit
    parameters.put("qty", String.valueOf(normalizedQuantity));
    parameters.put("orderType", "LIMIT");


    if (tpSlEnabled || enableFutures) {
      parameters.put("takeProfit", String.valueOf(takeProfitPrice));
      parameters.put("stopLoss", String.valueOf(stopLossPrice));
      parameters.put("tpOrderType", "Market");
      parameters.put("slOrderType", "Market");
    }
    return parameters;
  }

  public DbOrder sell(SYMBOL symbol, DbOrder openOrder, BigDecimal currentPrice, String notes) {

    if (openOrder == null || openOrder.isFutures()) {
      log.error("Open order is leverage, trust TP/SL");
      return null;
    }


    // Normalize quantity according to step size
    BigDecimal normalizedQuantity = openOrder.getQuantity().setScale(symbol.getQuantityDecimals(), RoundingMode.HALF_UP);
    normalizedQuantity = normalizedQuantity.multiply(new BigDecimal("0.999")).setScale(symbol.getQuantityDecimals(), RoundingMode.FLOOR);//fee 1%%

    openOrder.setQuantity(normalizedQuantity);

    Map<String, Object> parameters = getSellMap(symbol, currentPrice, normalizedQuantity);

    BigDecimal currentValue = normalizedQuantity.multiply(currentPrice);
    openOrder.setSoldPrice(currentPrice);
    openOrder.setSoldTimeStamp(new Date());
    openOrder.setSoldUSDValue(currentPrice.multiply(openOrder.getQuantity()));
    openOrder.setDelta(currentPrice.subtract(openOrder.getPrice()));
    openOrder.setDeltaUSD(openOrder.getDelta().multiply(normalizedQuantity));
    openOrder.setSide("SELL");
    openOrder.setSellReason(notes);

    log.info(">>> Creating sell order {} (dry={}) with price {} delta {}", symbol, dryOrders, currentPrice, openOrder.getDeltaUSD());
    if (!dryOrders) {
      if (bybitMarket.isUseThisExchange()) {

        bybitMarket.getBybitClient().createOrder(parameters, System.out::println);
      }
//      if (binanceMarket.isUseThisExchange()) {
//        binanceMarket.getBinanceClient().createTrade().newOrder(parameters);
//      }
      Toolkit.getDefaultToolkit().beep();
      if (persistOrders) {
        dbOrderRepo.save(openOrder);
      }

    }
    return openOrder;
  }

  @NotNull
  private Map<String, Object> getSellMap(SYMBOL symbol, BigDecimal currentPrice, BigDecimal normalizedQuantity) {

    Map<String, Object> parameters = new LinkedHashMap<>();
    parameters.put("symbol", symbol.getPair2());
    parameters.put("side", "SELL");
    parameters.put("category", "spot");

    //bybit
    parameters.put("qty", String.valueOf(normalizedQuantity));

//    parameters.put("recvWindow", 5000);
//    parameters.put("timestamp", System.currentTimeMillis());
//    parameters.put("type", "LIMIT");
//    parameters.put("quantity", normalizedQuantity);
//    parameters.put("qty", normalizedQuantity);

    if (useMarketPrice) {
//      parameters.put("type", "MARKET");
      parameters.put("orderType", "MARKET");
    } else {
//      parameters.put("type", "LIMIT");
      parameters.put("orderType", "LIMIT");
//      parameters.put("timeInForce", "GTC");
      parameters.put("price", currentPrice.setScale(symbol.getPriceDecimals(), RoundingMode.HALF_UP));
    }
    return parameters;
  }

  public String buyerTask(SYMBOL symbol) {
    log.debug("******************* Running Buyer task");
//    AssetBuyDTO buyDTOBinance = binanceMarket.checkBuyOrder(symbol);
//    log.debug("buyDTOBinance: {}", buyDTOBinance);
    AssetBuyDTO buyDTOBybit = bybitMarket.checkBuyOrder(symbol);
    log.debug("buyDTOBybit: {}", buyDTOBybit);
    return "Bybit: %s".formatted(buyDTOBybit);
  }

  public String sellerTask() {
    log.debug("******************* Running Seller task");
//    AssetSellDTO sellDTOBinance = binanceMarket.checkSellOrder();
//    log.debug("sellDTOBinance: {}", sellDTOBinance);
    AssetSellDTO sellDTOBybit = bybitMarket.checkSellOrder();
    log.debug("sellDTOBybit: {}", sellDTOBybit);
    return "Bybit: %s".formatted(sellDTOBybit);
  }


}