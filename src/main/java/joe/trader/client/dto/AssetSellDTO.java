package joe.trader.client.dto;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AssetSellDTO {
  private String asset;
  private boolean rsiSell;
  private boolean maSell;
  private boolean macdSell;
  private boolean priceSell;
  private boolean downtrend;
  private boolean alwaysSell;
  private boolean sell;
  private boolean dryOrders;
  private String sellReason;
  private String exchange;
  private BigDecimal balance;
  private BigDecimal price;
  private BigDecimal lowPrice;
  private BigDecimal highPrice;
}
