package joe.trader.client.dto;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AssetBuyDTO {
  private String asset;
  private boolean rsiBuy;
  private boolean maBuy;
  private boolean macdBuy;
  private boolean sarBuy;
  private boolean priceBuy;
  private boolean uptrend;
  private boolean alwaysBuy;
  private boolean buy;
  private boolean dryOrders;
  private String buyReason;
  private String exchange;
  private BigDecimal balance;
  private BigDecimal price;
  private BigDecimal lowPrice;
  private BigDecimal highPrice;
  private BigDecimal usdAvailable;
}