2025-04-12 22:10:21.216[1744510221216] | INFO  | main       | joe.trader.Application               - Starting Application using Java 24 with PID 53110 (/Users/<USER>/hb4e/joe.bi/build/classes/java/main started by jose.paz in /Users/<USER>/hb4e/joe.bi)
2025-04-12 22:10:21.217[1744510221217] | INFO  | main       | joe.trader.Application               - No active profile set, falling back to 1 default profile: "default"
2025-04-12 22:10:21.979[1744510221979] | INFO  | main       | o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-04-12 22:10:22.018[1744510222018] | INFO  | main       | o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 1 JPA repository interface.
2025-04-12 22:10:22.384[1744510222384] | INFO  | main       | o.s.b.w.e.tomcat.TomcatWebServer     - <PERSON><PERSON> initialized with port 8080 (http)
2025-04-12 22:10:22.396[1744510222396] | INFO  | main       | o.a.coyote.http11.Http11NioProtocol  - Initializing ProtocolHandler ["http-nio-8080"]
2025-04-12 22:10:22.397[1744510222397] | INFO  | main       | o.a.catalina.core.StandardService    - Starting service [Tomcat]
2025-04-12 22:10:22.397[1744510222397] | INFO  | main       | o.a.catalina.core.StandardEngine     - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-12 22:10:22.438[1744510222438] | INFO  | main       | o.a.c.c.C.[Tomcat].[localhost].[/]   - Initializing Spring embedded WebApplicationContext
2025-04-12 22:10:22.439[1744510222439] | INFO  | main       | o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1175 ms
2025-04-12 22:10:22.605[1744510222605] | INFO  | main       | o.h.jpa.internal.util.LogHelper      - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-04-12 22:10:22.639[1744510222639] | INFO  | main       | org.hibernate.Version                - HHH000412: Hibernate ORM core version 6.6.11.Final
2025-04-12 22:10:22.656[1744510222656] | INFO  | main       | o.h.c.i.RegionFactoryInitiator       - HHH000026: Second-level cache disabled
2025-04-12 22:10:22.988[1744510222988] | INFO  | main       | o.s.o.j.p.SpringPersistenceUnitInfo  - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-04-12 22:10:23.034[1744510223034] | INFO  | main       | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Starting...
2025-04-12 22:10:23.370[1744510223370] | INFO  | main       | com.zaxxer.hikari.pool.HikariPool    - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7717b4a0
2025-04-12 22:10:23.372[1744510223372] | INFO  | main       | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Start completed.
2025-04-12 22:10:23.436[1744510223436] | INFO  | main       | o.hibernate.orm.connections.pooling  - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 9.2
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-04-12 22:10:23.843[1744510223843] | INFO  | main       | o.h.e.t.j.p.i.JtaPlatformInitiator   - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-04-12 22:10:23.873[1744510223873] | INFO  | main       | o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-04-12 22:10:24.345[1744510224345] | WARN  | main       | o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-04-12 22:10:24.652[1744510224652] | INFO  | main       | o.a.coyote.http11.Http11NioProtocol  - Starting ProtocolHandler ["http-nio-8080"]
2025-04-12 22:10:24.662[1744510224662] | INFO  | main       | o.s.b.w.e.tomcat.TomcatWebServer     - Tomcat started on port 8080 (http) with context path '/'
2025-04-12 22:10:24.672[1744510224672] | INFO  | main       | joe.trader.Application               - Started Application in 3.808 seconds (process running for 5.11)
2025-04-12 22:10:29.303[1744510229303] | INFO  | http-nio-8080-exec-2 | o.a.c.c.C.[Tomcat].[localhost].[/]   - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-12 22:10:29.304[1744510229304] | INFO  | http-nio-8080-exec-2 | o.s.web.servlet.DispatcherServlet    - Initializing Servlet 'dispatcherServlet'
2025-04-12 22:10:29.305[1744510229305] | INFO  | http-nio-8080-exec-2 | o.s.web.servlet.DispatcherServlet    - Completed initialization in 1 ms
2025-04-12 22:10:29.410[1744510229410] | INFO  | http-nio-8080-exec-2 | j.trader.controller.HelloController  - dbStatus: DbStatus(id=163, timestamp=Sat Apr 12 22:10:29 GMT-04:00 2025)
2025-04-12 22:10:33.614[1744510233614] | INFO  | http-nio-8080-exec-3 | j.trader.controller.HelloController  - dbStatus: DbStatus(id=164, timestamp=Sat Apr 12 22:10:33 GMT-04:00 2025)
2025-04-12 22:11:43.465[1744510303465] | INFO  | SpringApplicationShutdownHook | o.s.b.w.e.tomcat.GracefulShutdown    - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-12 22:11:43.469[1744510303469] | INFO  | tomcat-shutdown | o.s.b.w.e.tomcat.GracefulShutdown    - Graceful shutdown complete
2025-04-12 22:11:43.475[1744510303475] | INFO  | SpringApplicationShutdownHook | o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-04-12 22:11:43.479[1744510303479] | INFO  | SpringApplicationShutdownHook | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Shutdown initiated...
2025-04-12 22:11:43.489[1744510303489] | INFO  | SpringApplicationShutdownHook | com.zaxxer.hikari.HikariDataSource   - HikariPool-1 - Shutdown completed.
