plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'joe'
version = '0.0.1'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
    maven { url='https://jitpack.io' }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.projectlombok:lombok:1.18.38'
    implementation 'io.github.binance:binance-connector-java:3.4.1'
    implementation 'com.google.guava:guava:33.4.7-jre'
    implementation 'org.apache.commons:commons-math3:3.6.1'
    implementation 'io.github.wuhewuhe:bybit-java-api:1.2.7'
    implementation 'com.h2database:h2:2.2.224'
    implementation 'io.github.natanimn:telebof:0.4.0-alpha'
    implementation 'org.jsoup:jsoup:1.21.1'
    implementation 'com.openai:openai-java:3.0.3'
    annotationProcessor 'org.projectlombok:lombok:1.18.38'
}

// Java compile options
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-Xlint:unchecked' << '-Xlint:deprecation'
}

// Enable Gradle build cache and parallel execution
// These can also be set in gradle.properties for global effect
if (project.hasProperty('org.gradle.caching')) {
    gradle.startParameter.buildCacheEnabled = true
}
gradle.startParameter.parallelProjectExecutionEnabled = true
